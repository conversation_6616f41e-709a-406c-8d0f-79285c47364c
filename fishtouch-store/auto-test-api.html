<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动API测试</title>
    <style>
        body {
            font-family: monospace;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .result {
            background: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <h1>🔧 自动API测试</h1>
    <p>此页面会在加载时自动执行API测试</p>
    
    <div id="result" class="result">正在初始化...</div>

    <script>
        function log(message, type = 'info') {
            const result = document.getElementById('result');
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 
                             type === 'warning' ? 'warning' : 'info';
            result.innerHTML += `<span class="${className}">${message}</span>\n`;
        }

        function clearLog() {
            document.getElementById('result').innerHTML = '';
        }

        async function runAutoTest() {
            clearLog();
            log('🚀 开始自动API测试...', 'info');
            
            // 1. 环境检测
            log('\n📋 步骤1: 环境检测', 'info');
            log(`window对象存在: ${typeof window !== 'undefined'}`, 'info');
            log(`__TAURI__存在: ${typeof window !== 'undefined' && !!window.__TAURI__}`, 'info');
            log(`User Agent: ${navigator.userAgent}`, 'info');
            
            const isTauri = typeof window !== 'undefined' && !!window.__TAURI__;
            if (isTauri) {
                log('✅ 检测到Tauri环境', 'success');
            } else {
                log('⚠️  未检测到Tauri环境，将使用Mock API', 'warning');
            }
            
            // 2. API导入测试
            log('\n📋 步骤2: API导入测试', 'info');
            let invoke = null;
            try {
                const tauriApi = await import("@tauri-apps/api/core");
                invoke = tauriApi.invoke;
                log('✅ Tauri API导入成功', 'success');
            } catch (error) {
                log(`❌ Tauri API导入失败: ${error.message}`, 'error');
            }
            
            // 3. 直接API调用测试
            log('\n📋 步骤3: 直接API调用测试', 'info');
            if (invoke) {
                try {
                    log('🔧 调用get_all_packages...', 'info');
                    const packages = await invoke("get_all_packages");
                    log(`✅ get_all_packages成功: ${JSON.stringify(packages, null, 2)}`, 'success');
                    
                    log('🔧 调用get_all_items...', 'info');
                    const items = await invoke("get_all_items");
                    log(`✅ get_all_items成功: ${JSON.stringify(items, null, 2)}`, 'success');
                    
                } catch (error) {
                    log(`❌ 直接API调用失败: ${error.message}`, 'error');
                }
            } else {
                log('⚠️  跳过直接API调用（invoke不可用）', 'warning');
            }
            
            // 4. 通过服务层API调用测试
            log('\n📋 步骤4: 服务层API调用测试', 'info');
            try {
                // 模拟服务层API调用
                log('🔧 模拟packageApi.getAll()调用...', 'info');
                
                // 这里我们需要模拟API服务的逻辑
                const mockApiCall = async (command) => {
                    if (invoke) {
                        log(`🔧 使用真实Tauri API: ${command}`, 'info');
                        return await invoke(command);
                    } else {
                        log(`🔧 使用Mock API: ${command}`, 'warning');
                        // Mock数据
                        if (command === "get_all_packages") {
                            return [
                                { id: 999, name: "[MOCK] 测试套餐1", description: "这是Mock数据" },
                                { id: 998, name: "[MOCK] 测试套餐2", description: "这是Mock数据" }
                            ];
                        } else if (command === "get_all_items") {
                            return [
                                { id: 999, name: "[MOCK] 测试单品1", sku: "MOCK001" },
                                { id: 998, name: "[MOCK] 测试单品2", sku: "MOCK002" }
                            ];
                        }
                        return [];
                    }
                };
                
                const packages = await mockApiCall("get_all_packages");
                log(`✅ 套餐数据: ${JSON.stringify(packages, null, 2)}`, 'success');
                
                const items = await mockApiCall("get_all_items");
                log(`✅ 单品数据: ${JSON.stringify(items, null, 2)}`, 'success');
                
            } catch (error) {
                log(`❌ 服务层API调用失败: ${error.message}`, 'error');
            }
            
            // 5. 总结
            log('\n📋 测试总结', 'info');
            if (isTauri && invoke) {
                log('✅ 完全成功：在Tauri环境中使用真实API', 'success');
            } else if (!isTauri) {
                log('⚠️  部分成功：在浏览器环境中使用Mock API', 'warning');
                log('💡 建议：在Tauri桌面应用中打开此页面以使用真实API', 'info');
            } else {
                log('❌ 失败：Tauri环境检测到但API不可用', 'error');
            }
            
            log('\n🔧 测试完成！', 'info');
        }

        // 页面加载后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runAutoTest, 1000);
        });
    </script>
</body>
</html>
