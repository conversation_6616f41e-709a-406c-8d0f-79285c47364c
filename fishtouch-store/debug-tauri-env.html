<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tauri环境检测</title>
    <style>
        body {
            font-family: monospace;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .result {
            background: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🔧 Tauri环境检测工具</h1>
    
    <button onclick="checkEnvironment()">检查环境</button>
    <button onclick="testApiImport()">测试API导入</button>
    <button onclick="testDirectCall()">测试直接调用</button>
    
    <div id="result" class="result"></div>

    <script>
        function log(message, type = 'info') {
            const result = document.getElementById('result');
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 
                             type === 'warning' ? 'warning' : 'info';
            result.innerHTML += `<span class="${className}">${message}</span>\n`;
        }

        function clearLog() {
            document.getElementById('result').innerHTML = '';
        }

        async function checkEnvironment() {
            clearLog();
            log('🔧 开始环境检测...', 'info');
            
            // 检查window对象
            log(`window对象存在: ${typeof window !== 'undefined'}`, 'info');
            
            // 检查__TAURI__
            log(`window.__TAURI__存在: ${typeof window !== 'undefined' && !!window.__TAURI__}`, 'info');
            
            if (typeof window !== 'undefined' && window.__TAURI__) {
                log('✅ 检测到Tauri环境', 'success');
                log(`__TAURI__内容: ${JSON.stringify(Object.keys(window.__TAURI__), null, 2)}`, 'info');
            } else {
                log('❌ 未检测到Tauri环境', 'error');
                log('这可能是以下原因之一:', 'warning');
                log('1. 在浏览器中打开而不是Tauri应用中', 'warning');
                log('2. Tauri API未正确加载', 'warning');
                log('3. 应用未正确初始化', 'warning');
            }
            
            // 检查其他Tauri相关对象
            if (typeof window !== 'undefined') {
                const tauriKeys = Object.keys(window).filter(key => key.includes('tauri') || key.includes('TAURI'));
                log(`Tauri相关的window属性: ${JSON.stringify(tauriKeys)}`, 'info');
            }
        }

        async function testApiImport() {
            clearLog();
            log('🔧 测试API导入...', 'info');
            
            try {
                log('尝试导入@tauri-apps/api/core...', 'info');
                const tauriApi = await import("@tauri-apps/api/core");
                log('✅ API导入成功', 'success');
                log(`导入的API对象: ${JSON.stringify(Object.keys(tauriApi), null, 2)}`, 'info');
                
                if (tauriApi.invoke) {
                    log('✅ invoke函数存在', 'success');
                    window.testInvoke = tauriApi.invoke;
                } else {
                    log('❌ invoke函数不存在', 'error');
                }
                
            } catch (error) {
                log(`❌ API导入失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        }

        async function testDirectCall() {
            clearLog();
            log('🔧 测试直接API调用...', 'info');
            
            if (!window.testInvoke) {
                log('❌ 请先运行"测试API导入"', 'error');
                return;
            }
            
            try {
                log('调用get_all_packages...', 'info');
                const packages = await window.testInvoke("get_all_packages");
                log('✅ API调用成功', 'success');
                log(`返回数据: ${JSON.stringify(packages, null, 2)}`, 'info');
                
                log('调用get_all_items...', 'info');
                const items = await window.testInvoke("get_all_items");
                log('✅ API调用成功', 'success');
                log(`返回数据: ${JSON.stringify(items, null, 2)}`, 'info');
                
            } catch (error) {
                log(`❌ API调用失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>
