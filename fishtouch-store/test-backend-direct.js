#!/usr/bin/env node

/**
 * 直接测试后端API调用
 * 通过SQLite直接查询和Tauri命令对比
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 执行SQLite查询
function executeSqlite(query) {
    return new Promise((resolve, reject) => {
        const dbPath = path.join(process.cwd(), 'src-tauri', 'database.db');
        const sqlite = spawn('sqlite3', [dbPath, query], {
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let output = '';
        let errorOutput = '';
        
        sqlite.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        sqlite.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });
        
        sqlite.on('close', (code) => {
            if (code === 0) {
                resolve(output.trim());
            } else {
                reject(new Error(errorOutput || `SQLite exited with code ${code}`));
            }
        });
    });
}

// 检查数据库内容
async function checkDatabaseContent() {
    log('\n🔧 检查数据库内容', 'cyan');
    log('=' .repeat(50), 'cyan');
    
    try {
        // 检查套餐
        const packageCount = await executeSqlite('SELECT COUNT(*) FROM packages;');
        const packageData = await executeSqlite('SELECT id, name, description FROM packages;');
        
        log(`✅ 套餐数量: ${packageCount}`, 'green');
        if (packageData) {
            log('📋 套餐数据:', 'blue');
            const lines = packageData.split('\n');
            lines.forEach(line => {
                if (line.trim()) {
                    const [id, name, description] = line.split('|');
                    log(`  ID:${id} 名称:${name} 描述:${description || '无'}`, 'blue');
                }
            });
        }
        
        // 检查单品
        const itemCount = await executeSqlite('SELECT COUNT(*) FROM items;');
        const itemData = await executeSqlite('SELECT id, name, sku FROM items;');
        
        log(`✅ 单品数量: ${itemCount}`, 'green');
        if (itemData) {
            log('📋 单品数据:', 'blue');
            const lines = itemData.split('\n');
            lines.forEach(line => {
                if (line.trim()) {
                    const [id, name, sku] = line.split('|');
                    log(`  ID:${id} 名称:${name} SKU:${sku}`, 'blue');
                }
            });
        }
        
        return {
            packageCount: parseInt(packageCount),
            itemCount: parseInt(itemCount),
            hasData: parseInt(packageCount) > 0 || parseInt(itemCount) > 0
        };
        
    } catch (error) {
        log(`❌ 数据库查询失败: ${error.message}`, 'red');
        return { packageCount: 0, itemCount: 0, hasData: false };
    }
}

// 检查Tauri应用是否运行
function checkTauriRunning() {
    log('\n🔧 检查Tauri应用状态', 'cyan');
    log('=' .repeat(50), 'cyan');
    
    return new Promise((resolve) => {
        const curl = spawn('curl', ['-s', 'http://localhost:1420/'], {
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        curl.on('close', (code) => {
            if (code === 0) {
                log('✅ Tauri开发服务器正在运行 (localhost:1420)', 'green');
                resolve(true);
            } else {
                log('❌ Tauri开发服务器未运行', 'red');
                log('💡 请运行: tauri dev', 'yellow');
                resolve(false);
            }
        });
    });
}

// 检查API服务文件
function checkApiServiceFile() {
    log('\n🔧 检查API服务文件', 'cyan');
    log('=' .repeat(50), 'cyan');
    
    try {
        const apiFilePath = path.join(process.cwd(), 'src', 'services', 'api.ts');
        const apiContent = fs.readFileSync(apiFilePath, 'utf8');
        
        // 检查关键函数
        const checks = [
            {
                name: 'packageApi.getAll',
                pattern: /packageApi[\s\S]*?getAll[\s\S]*?get_all_packages/,
                description: '套餐获取API'
            },
            {
                name: 'itemApi.getAll', 
                pattern: /itemApi[\s\S]*?getAll[\s\S]*?get_all_items/,
                description: '单品获取API'
            },
            {
                name: 'apiInvoke函数',
                pattern: /const apiInvoke = async/,
                description: 'API调用函数'
            },
            {
                name: '错误处理',
                pattern: /if \(typeof result === "string"\)/,
                description: '错误处理逻辑'
            }
        ];
        
        let passedChecks = 0;
        
        checks.forEach(check => {
            if (check.pattern.test(apiContent)) {
                log(`✅ ${check.name}: ${check.description}`, 'green');
                passedChecks++;
            } else {
                log(`❌ ${check.name}: ${check.description}`, 'red');
            }
        });
        
        log(`📊 API服务检查: ${passedChecks}/${checks.length} 项通过`, passedChecks === checks.length ? 'green' : 'yellow');
        
        return passedChecks === checks.length;
        
    } catch (error) {
        log(`❌ 检查API服务文件失败: ${error.message}`, 'red');
        return false;
    }
}

// 检查前端页面文件
function checkFrontendPages() {
    log('\n🔧 检查前端页面文件', 'cyan');
    log('=' .repeat(50), 'cyan');
    
    try {
        // 检查套餐管理页面
        const packagePagePath = path.join(process.cwd(), 'src', 'pages', 'PackageManagement.tsx');
        const packageContent = fs.readFileSync(packagePagePath, 'utf8');
        
        const packageChecks = [
            /fetchPackages.*packageApi\.getAll/,
            /useEffect.*fetchPackages/,
            /setPackages\(data\)/
        ];
        
        const packagePassed = packageChecks.every(check => check.test(packageContent));
        log(`${packagePassed ? '✅' : '❌'} 套餐管理页面: ${packagePassed ? '正常' : '有问题'}`, packagePassed ? 'green' : 'red');
        
        // 检查单品管理页面
        const itemPagePath = path.join(process.cwd(), 'src', 'pages', 'ItemManagement.tsx');
        const itemContent = fs.readFileSync(itemPagePath, 'utf8');
        
        const itemChecks = [
            /fetchItems.*itemApi\.getAll/,
            /useEffect.*fetchItems/,
            /setItems\(data\)/
        ];
        
        const itemPassed = itemChecks.every(check => check.test(itemContent));
        log(`${itemPassed ? '✅' : '❌'} 单品管理页面: ${itemPassed ? '正常' : '有问题'}`, itemPassed ? 'green' : 'red');
        
        return packagePassed && itemPassed;
        
    } catch (error) {
        log(`❌ 检查前端页面失败: ${error.message}`, 'red');
        return false;
    }
}

// 生成诊断报告
function generateDiagnosticReport(results) {
    log('\n📋 诊断报告', 'magenta');
    log('=' .repeat(60), 'magenta');
    
    const { dbResult, tauriRunning, apiServiceOk, frontendPagesOk } = results;
    
    log('\n📊 检查结果:', 'blue');
    log(`数据库内容: ${dbResult.hasData ? '✅ 有数据' : '❌ 无数据'}`, dbResult.hasData ? 'green' : 'red');
    log(`  - 套餐: ${dbResult.packageCount} 个`, 'blue');
    log(`  - 单品: ${dbResult.itemCount} 个`, 'blue');
    log(`Tauri服务器: ${tauriRunning ? '✅ 运行中' : '❌ 未运行'}`, tauriRunning ? 'green' : 'red');
    log(`API服务: ${apiServiceOk ? '✅ 正常' : '❌ 有问题'}`, apiServiceOk ? 'green' : 'red');
    log(`前端页面: ${frontendPagesOk ? '✅ 正常' : '❌ 有问题'}`, frontendPagesOk ? 'green' : 'red');
    
    log('\n🔍 问题分析:', 'yellow');
    
    if (!dbResult.hasData) {
        log('❌ 数据库中没有数据 - 这是主要问题！', 'red');
        log('💡 建议: 检查数据创建是否真的成功', 'yellow');
    } else if (!tauriRunning) {
        log('❌ Tauri服务器未运行', 'red');
        log('💡 建议: 运行 tauri dev 启动开发服务器', 'yellow');
    } else if (!apiServiceOk) {
        log('❌ API服务有问题', 'red');
        log('💡 建议: 检查 src/services/api.ts 文件', 'yellow');
    } else if (!frontendPagesOk) {
        log('❌ 前端页面有问题', 'red');
        log('💡 建议: 检查页面组件的数据获取逻辑', 'yellow');
    } else {
        log('🤔 所有检查都通过，但仍然看不到数据', 'yellow');
        log('💡 建议: 检查浏览器控制台日志，可能是前端渲染问题', 'yellow');
    }
    
    log('\n🛠️  下一步调试建议:', 'cyan');
    log('1. 在浏览器中打开 http://localhost:1420/debug-api-direct.html', 'cyan');
    log('2. 点击"检查Tauri环境"和"获取所有套餐/单品"', 'cyan');
    log('3. 查看浏览器控制台的详细日志', 'cyan');
    log('4. 检查Tauri后端终端的输出日志', 'cyan');
}

// 主函数
async function main() {
    log('🚀 FishTouch Store 后端诊断工具', 'bright');
    log('=' .repeat(60), 'bright');
    
    const dbResult = await checkDatabaseContent();
    const tauriRunning = await checkTauriRunning();
    const apiServiceOk = checkApiServiceFile();
    const frontendPagesOk = checkFrontendPages();
    
    generateDiagnosticReport({
        dbResult,
        tauriRunning,
        apiServiceOk,
        frontendPagesOk
    });
}

// 运行诊断
main().catch(error => {
    log(`\n❌ 诊断过程出错: ${error.message}`, 'red');
    process.exit(1);
});
