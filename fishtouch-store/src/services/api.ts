import type {
  Package, NewPackage,
  Item, NewItem,
  Recipe, NewRecipe,
  Batch, NewBatch,
  PurchaseOrder, NewPurchaseOrder,
  ShipmentOrder, NewShipmentOrder,
  InventoryTransaction,
  StockLevel,
  LowStockAlert,
  ApiResult
} from "../types";

// 环境检测和Tauri API导入
let invoke: any;

// 动态导入Tauri API
const initTauriApi = async () => {
  try {
    // 检测是否在Tauri环境中
    if (typeof window !== 'undefined' && (window as any).__TAURI__) {
      const tauriApi = await import("@tauri-apps/api/core");
      invoke = tauriApi.invoke;
    }
  } catch (error) {
    console.warn("Tauri API not available, running in browser mode");
  }
};

// 初始化API
initTauriApi();

// 浏览器环境下的mock实现
const mockInvoke = async (command: string, args?: any): Promise<any> => {
  console.warn(`Mock API call: ${command}`, args);

  // 根据命令返回mock数据
  switch (command) {
    case "get_all_packages":
      return [];
    case "get_all_items":
      return [];
    case "get_recipes_by_package_id":
      return [];
    case "get_batches_by_item_id":
      return [];
    case "get_all_recipes":
      return [];
    case "get_all_batches":
      return [];
    case "get_all_stock_levels":
      return [];
    case "check_low_stock_alerts":
      return [];
    case "create_sample_data":
      return "Sample data created successfully (mock)";
    case "create_package":
      return { id: Math.floor(Math.random() * 1000), name: args.name, description: args.description };
    case "create_item":
      return { id: Math.floor(Math.random() * 1000), name: args.name, sku: args.sku };
    case "create_recipe":
      return { id: Math.floor(Math.random() * 1000), ...args };
    case "create_batch":
      return { id: Math.floor(Math.random() * 1000), ...args };
    default:
      throw new Error(`Mock API: Command ${command} not implemented in browser mode`);
  }
};

// 使用适当的invoke函数
const apiInvoke = invoke || mockInvoke;

// 套餐管理 API
export const packageApi = {
  async create(data: NewPackage): Promise<Package> {
    const result: ApiResult<Package> = await apiInvoke("create_package", {
      name: data.name,
      description: data.description
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Package[]> {
    const result: ApiResult<Package[]> = await apiInvoke("get_all_packages");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Package> {
    const result: ApiResult<Package> = await apiInvoke("get_package_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: NewPackage): Promise<Package> {
    const result: ApiResult<Package> = await apiInvoke("update_package", {
      id,
      name: data.name,
      description: data.description
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await apiInvoke("delete_package", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 单品管理 API
export const itemApi = {
  async create(data: NewItem): Promise<Item> {
    const result: ApiResult<Item> = await apiInvoke("create_item", {
      name: data.name,
      sku: data.sku
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Item[]> {
    const result: ApiResult<Item[]> = await apiInvoke("get_all_items");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Item> {
    const result: ApiResult<Item> = await apiInvoke("get_item_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getBySku(sku: string): Promise<Item> {
    const result: ApiResult<Item> = await apiInvoke("get_item_by_sku", { sku });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: Partial<NewItem>): Promise<Item> {
    const result: ApiResult<Item> = await apiInvoke("update_item", {
      id,
      name: data.name,
      sku: data.sku
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await apiInvoke("delete_item", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 配方管理 API
export const recipeApi = {
  async create(data: NewRecipe): Promise<Recipe> {
    const result: ApiResult<Recipe> = await apiInvoke("create_recipe", {
      package_id: data.package_id,
      item_id: data.item_id,
      quantity: data.quantity,
      unit: data.unit,
      valid_from: data.valid_from,
      valid_to: data.valid_to
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getByPackageId(packageId: number): Promise<Recipe[]> {
    const result: ApiResult<Recipe[]> = await apiInvoke("get_recipes_by_package_id", { package_id: packageId });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Recipe[]> {
    const result: ApiResult<Recipe[]> = await apiInvoke("get_all_recipes");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Recipe> {
    const result: ApiResult<Recipe> = await apiInvoke("get_recipe_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: Partial<NewRecipe>): Promise<Recipe> {
    const result: ApiResult<Recipe> = await apiInvoke("update_recipe", {
      id,
      quantity: data.quantity,
      unit: data.unit,
      valid_from: data.valid_from,
      valid_to: data.valid_to
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await apiInvoke("delete_recipe", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 批次管理 API
export const batchApi = {
  async create(data: NewBatch): Promise<Batch> {
    const result: ApiResult<Batch> = await apiInvoke("create_batch", {
      item_id: data.item_id,
      batch_number: data.batch_number,
      in_date: data.in_date,
      expiry_date: data.expiry_date
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getByItemId(itemId: number): Promise<Batch[]> {
    const result: ApiResult<Batch[]> = await apiInvoke("get_batches_by_item_id", { item_id: itemId });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Batch[]> {
    const result: ApiResult<Batch[]> = await apiInvoke("get_all_batches");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Batch> {
    const result: ApiResult<Batch> = await apiInvoke("get_batch_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: Partial<NewBatch>): Promise<Batch> {
    const result: ApiResult<Batch> = await apiInvoke("update_batch", {
      id,
      batch_number: data.batch_number,
      in_date: data.in_date,
      expiry_date: data.expiry_date
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await apiInvoke("delete_batch", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 采购订单 API
export const purchaseOrderApi = {
  async create(data: NewPurchaseOrder): Promise<PurchaseOrder> {
    const result: ApiResult<PurchaseOrder> = await apiInvoke("create_purchase_order", {
      order_number: data.order_number,
      order_date: data.order_date,
      supplier: data.supplier
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 发货订单 API
export const shipmentOrderApi = {
  async create(data: NewShipmentOrder): Promise<ShipmentOrder> {
    const result: ApiResult<ShipmentOrder> = await apiInvoke("create_shipment_order", {
      order_number: data.order_number,
      order_date: data.order_date,
      customer: data.customer
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 库存管理 API
export const inventoryApi = {
  async recordPurchaseTransaction(
    batchId: number,
    quantity: number,
    orderNumber: string,
    timestamp: string
  ): Promise<InventoryTransaction> {
    const result: ApiResult<InventoryTransaction> = await apiInvoke("record_purchase_transaction", {
      batch_id: batchId,
      quantity,
      order_number: orderNumber,
      timestamp
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async recordShipmentTransaction(
    batchId: number,
    quantity: number,
    orderNumber: string,
    timestamp: string
  ): Promise<InventoryTransaction> {
    const result: ApiResult<InventoryTransaction> = await apiInvoke("record_shipment_transaction", {
      batch_id: batchId,
      quantity,
      order_number: orderNumber,
      timestamp
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getCurrentStockForItem(itemId: number): Promise<StockLevel> {
    const result: ApiResult<StockLevel> = await apiInvoke("get_current_stock_for_item", { item_id: itemId });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAllStockLevels(): Promise<StockLevel[]> {
    const result: ApiResult<StockLevel[]> = await apiInvoke("get_all_stock_levels");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async checkLowStockAlerts(): Promise<LowStockAlert[]> {
    const result: ApiResult<LowStockAlert[]> = await apiInvoke("check_low_stock_alerts");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 测试数据 API
export const testApi = {
  async createSampleData(): Promise<string> {
    const result: ApiResult<string> = await apiInvoke("create_sample_data");
    if (typeof result === "string" && result.includes("error")) {
      throw new Error(result);
    }
    return result as string;
  }
};
