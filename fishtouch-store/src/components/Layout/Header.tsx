
import {
  IconButton,
  Flex,
  Text,
  FlexProps,
} from "@chakra-ui/react";
import { Menu as MenuIcon } from "lucide-react";

interface HeaderProps extends FlexProps {
  onOpen?: () => void;
}

const Header = ({ onOpen, ...rest }: HeaderProps) => {
  return (
    <Flex
      ml={{ base: 0, md: 60 }}
      px={{ base: 4, md: 4 }}
      height="20"
      alignItems="center"
      bg="white"
      borderBottomWidth="1px"
      borderBottomColor="gray.200"
      justifyContent={{ base: "space-between", md: "flex-end" }}
      {...rest}
    >
      <IconButton
        display={{ base: "flex", md: "none" }}
        onClick={onOpen}
        variant="outline"
        aria-label="open menu"
      >
        <MenuIcon />
      </IconButton>

      <Text
        display={{ base: "flex", md: "none" }}
        fontSize="2xl"
        fontFamily="monospace"
        fontWeight="bold"
      >
        FishTouch Store
      </Text>
    </Flex>
  );
};

export default Header;
