mod database;
mod models;
mod schema;
mod services;
mod commands;
mod tests;

use commands::*;
use database::create_pool;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let db_pool = create_pool();

    // Run migrations on startup
    {
        let mut conn = db_pool.lock().unwrap();
        if let Err(e) = database::run_migrations(&mut *conn) {
            eprintln!("Failed to run migrations: {}", e);
        }
    }

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(db_pool)
        .invoke_handler(tauri::generate_handler![
            greet,
            // Package commands
            create_package,
            get_all_packages,
            get_package_by_id,
            update_package,
            delete_package,
            // Item commands
            create_item,
            get_all_items,
            get_item_by_id,
            get_item_by_sku,
            update_item,
            delete_item,
            // Recipe commands
            create_recipe,
            get_recipes_by_package_id,
            get_all_recipes,
            get_recipe_by_id,
            update_recipe,
            delete_recipe,
            // Batch commands
            create_batch,
            get_batches_by_item_id,
            get_all_batches,
            get_batch_by_id,
            update_batch,
            delete_batch,
            // Inventory commands
            create_purchase_order,
            create_shipment_order,
            record_purchase_transaction,
            record_shipment_transaction,
            get_current_stock_for_item,
            get_all_stock_levels,
            check_low_stock_alerts,
            // Test commands
            create_sample_data
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
