use diesel::prelude::*;
use diesel::sqlite::SqliteConnection;
use std::sync::{Arc, Mutex};
use std::env;

pub type DbConnection = SqliteConnection;
pub type DbPool = Arc<Mutex<DbConnection>>;

pub fn establish_connection() -> DbConnection {
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "database.db".to_string());

    // Ensure we use an absolute path for the database
    let database_path = if database_url.starts_with("sqlite:") {
        database_url
    } else {
        format!("sqlite:{}", database_url)
    };

    println!("Connecting to database: {}", database_path);

    let connection = SqliteConnection::establish(&database_path)
        .unwrap_or_else(|e| panic!("Error connecting to {}: {}", database_path, e));

    println!("Database connection established successfully");
    connection
}

pub fn create_pool() -> DbPool {
    let connection = establish_connection();
    Arc::new(Mutex::new(connection))
}

// Helper function to run migrations programmatically
pub fn run_migrations(connection: &mut SqliteConnection) -> Result<(), Box<dyn std::error::Error + Send + Sync + 'static>> {
    use diesel_migrations::{embed_migrations, EmbeddedMigrations, MigrationHarness};
    
    const MIGRATIONS: EmbeddedMigrations = embed_migrations!("migrations");
    connection.run_pending_migrations(MIGRATIONS)?;
    Ok(())
}

#[cfg(test)]
pub fn establish_test_connection() -> DbConnection {
    let database_url = ":memory:";
    let mut connection = SqliteConnection::establish(database_url)
        .unwrap_or_else(|_| panic!("Error connecting to test database"));
    
    // Run migrations for test database
    run_migrations(&mut connection).expect("Failed to run migrations");
    connection
}
